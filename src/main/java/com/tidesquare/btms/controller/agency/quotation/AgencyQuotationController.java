package com.tidesquare.btms.controller.agency.quotation;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.agency.quotation.parse_email.ParseEmailBody;
import com.tidesquare.btms.controller.agency.quotation.parse_email.ParseEmailHandler;
import com.tidesquare.btms.filter.AgencyBinding;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;

@Path("agency/quotation")
@AgencyBinding
@SecurityRequirement(name = "bearer")
public class AgencyQuotationController {

    @Inject
    private ParseEmailHandler parseEmailHandler;

    @POST
    @Path("/parse-email")
    @Operation(summary = "Emergency Register PNR")
    public ApiResponse<Void> emegencyRegister(@Valid ParseEmailBody body) {
        this.parseEmailHandler.run(body);
        return ApiResponse.fromData(null);
    }
}
