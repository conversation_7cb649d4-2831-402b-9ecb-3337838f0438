package com.tidesquare.btms.controller.agency.quotation.parse_email;

import com.tidesquare.btms.service.ai.AIService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class ParseEmailHandler {

    @Inject
    private AIService aiService;

    public void run(ParseEmailBody body) {
        this.aiService.parseEmailQuotation(body.getEmailContent());
    }
}
