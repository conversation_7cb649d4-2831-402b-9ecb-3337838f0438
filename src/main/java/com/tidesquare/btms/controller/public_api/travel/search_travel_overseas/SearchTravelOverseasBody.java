package com.tidesquare.btms.controller.public_api.travel.search_travel_overseas;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

import com.tidesquare.btms.constant.SeatClass;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Getter
@Setter
public class SearchTravelOverseasBody {
    @Size(min = 1)
    @Valid
    @NotNull
    List<SearchTravelOverseasJourney> journeys;

    @Min(value = 1)
    private int adultCount;

    @NotNull
    private SeatClass cabinClass;

    @NotNull
    private Long companyId;
}
