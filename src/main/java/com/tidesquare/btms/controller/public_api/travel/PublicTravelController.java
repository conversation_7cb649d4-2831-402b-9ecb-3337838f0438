package com.tidesquare.btms.controller.public_api.travel;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.agency.travel.search_travel_overseas.SearchTravelOverseasBody;
import com.tidesquare.btms.controller.agency.travel.search_travel_overseas.SearchTravelOverseasHandler;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.response.SearchFareScheduleResponse;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;

@Path("public/travel")
public class PublicTravelController {

    @Inject
    private SearchTravelOverseasHandler searchTravelOverseasHandler;

    @POST
    @Path("/overseas/search")
    @Operation(summary = "Search Travel Overseas")
    public ApiResponse<SearchFareScheduleResponse> search(@Valid SearchTravelOverseasBody body) {
        return ApiResponse.fromData(this.searchTravelOverseasHandler.run(body));
    }
}
