package com.tidesquare.btms.controller.public_api.travel.search_travel_overseas;

import java.util.List;
import com.tidesquare.btms.service.stella.dto.request.JourneyReq;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.response.SearchFareScheduleResponse;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class SearchTravelOverseasHandler {

    @Inject
    private TravelService travelService;

    public SearchFareScheduleResponse run(SearchTravelOverseasBody body) {
        List<JourneyReq> journeyInfoDtos = body.getJourneys().stream()
                .map(x -> JourneyReq.builder()
                        .deptAirport(x.getDeptAirport())
                        .arrAirport(x.getArrAirport())
                        .departureDate(x.getDepartureDate())
                        .build())
                .toList();

        return this.travelService.searchOverseas(body.getCompanyId(), journeyInfoDtos, body.getAdultCount(), body.getCabinClass());
    }
}
