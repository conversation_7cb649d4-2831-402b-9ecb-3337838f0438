package com.tidesquare.btms.controller.public_api.travel.search_travel_overseas;

import com.tidesquare.btms.validator.ValidDateString;

import io.smallrye.common.constraint.NotNull;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SearchTravelOverseasJourney {
    @NotNull
    @ValidDateString(format = "yyyy-MM-dd")
    private String departureDate;

    @NotBlank
    private String deptAirport;

    @NotBlank
    private String arrAirport;
}
