package com.tidesquare.btms.service.ai;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;

import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.ChatModel;
import com.openai.models.responses.ResponseCreateParams;
import com.openai.models.responses.StructuredResponseCreateParams;
import com.openai.models.responses.StructuredResponseOutputItem;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
@Startup
public class AIService {

    @ConfigProperty(name = "OPENAI_API_KEY", defaultValue = "")
    private String openaiApiKey;

    private OpenAIClient client;

    @PostConstruct
    public void init() {
        this.client = OpenAIOkHttpClient.builder().apiKey(openaiApiKey).build();
    }

    public BusinessTrip parseEmailQuotation(String emailContent) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy. M. d. a hh:mm:ss", Locale.KOREA);
        String formatted = now.format(formatter);

        String prompt = "현재 시간은 " + formatted + "입니다. 다음은 회사 직원이 보낸 출장 항공권 예약 요청 이메일입니다. 이 이메일에서 출장 정보를 추출해 주세요.\n```\n" + emailContent + "\n```";

        StructuredResponseCreateParams<BusinessTrip> params = ResponseCreateParams.builder()
                .input(prompt)
                .model(ChatModel.O4_MINI)
                .text(BusinessTrip.class)
                .build();

        List<StructuredResponseOutputItem<BusinessTrip>> response = this.client.responses().create(params).output();
        BusinessTrip businessTrip = response.getFirst().message().get().content().getFirst().asOutputText();
        return businessTrip;
    }
}
