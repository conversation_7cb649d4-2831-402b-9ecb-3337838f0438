package com.tidesquare.btms.service.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;

public class Journey {
    @JsonPropertyDescription("출발 공항의 IATA 코드를 입력해 주세요. 출발 공항을 정확히 알 수 없는 경우, 출발 도시의 IATA 코드를 사용해 주세요.")
    @JsonProperty("departure_airport")
    private String departureAirport;
    @JsonPropertyDescription("도착 공항의 IATA 코드를 입력해 주세요. 도착 공항을 정확히 알 수 없는 경우, 도착 도시의 IATA 코드를 사용해 주세요.")
    @JsonProperty("arrival_airport")
    private String arrivalAirport;
    @JsonPropertyDescription("출발 날짜는 YYYY-MM-DD 형식으로 입력해 주세요.")
    @JsonProperty("departure_date")
    private String departureDate;
}
