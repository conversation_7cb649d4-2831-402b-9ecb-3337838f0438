package com.tidesquare.btms.service.ai;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;

public class BusinessTrip {
    @JsonPropertyDescription("항공 여정 정보 목록입니다.")
    @JsonProperty("journeys")
    private List<Journey> journeys;
    @JsonPropertyDescription("승객 수입니다.")
    @JsonProperty("passenger_count")
    private int passengerCount;
    @JsonPropertyDescription("좌석 등급입니다. 가능한 값은 다음과 같습니다: 일반석, 프리미엄 이코노미, 비즈니스, 일등석. 좌석 등급을 확인할 수 없는 경우 '일반석'를 사용해 주세요.")
    @JsonProperty("seat_class")
    private String seatClass;
    @JsonPropertyDescription("항공사 IATA 코드입니다. 항공사를 확인할 수 없는 경우 'Any'를 사용해 주세요.")
    @JsonProperty("airline")
    private String airline;
    @JsonPropertyDescription("회사 이름을 한글로 작성해 주세요. 회사 이름을 확인할 수 없는 경우 'Any'를 사용해 주세요.")
    @JsonProperty("company_name")
    private String companyName;
    @JsonPropertyDescription("추가 요청 목록입니다. 가능한 값은 다음과 같습니다: 저가, 직항. 추가 요청이 없는 경우 빈 배열([])을 사용해 주세요.")
    @JsonProperty("additional_requests")
    private List<String> additionalRequests;
}
